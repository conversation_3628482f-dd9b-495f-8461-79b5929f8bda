# 服务时长统计模块接口文档

## 概述

服务时长统计模块用于记录和统计每个服务项目和增项服务的具体用时，员工可以在每个服务节点点击开始和结束，系统会自动计算服务时长并更新平均值。

## 时长类型说明

### 1. 总服务时长（Total Service Duration）
- **定义**：订单中所有服务项目和增项服务的累计时长
- **计算方式**：所有已完成服务记录的时长之和
- **存储位置**：`orders.actualServiceDuration` 字段
- **用途**：统计订单的实际服务工作量

### 2. 服务跨度时长（Service Span Duration）
- **定义**：从第一个服务开始到最后一个服务结束的总时间
- **计算方式**：最晚结束时间 - 最早开始时间
- **用途**：了解订单的实际服务周期

### 3. 服务项时长（Main Service Duration）
- **定义**：单个主服务（如洗护、美容）的具体用时
- **记录方式**：通过 `ServiceDurationRecord` 表记录
- **用途**：优化服务流程，更新服务平均时长

### 4. 增项时长（Additional Service Duration）
- **定义**：增项服务（如刷牙、指甲修剪）的具体用时
- **记录方式**：通过 `ServiceDurationRecord` 表记录
- **控制字段**：`additional_services.needDurationTracking` 控制是否需要统计时长
- **用途**：精细化管理增项服务效率

## 数据模型

### 服务时长记录表 (service_duration_records)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 记录ID |
| orderId | INTEGER | 关联订单ID |
| orderDetailId | INTEGER | 关联订单详情ID（主服务时使用） |
| additionalServiceOrderId | INTEGER | 关联追加服务订单ID（增项服务时使用） |
| employeeId | INTEGER | 关联员工ID |
| recordType | ENUM | 记录类型：main_service/additional_service |
| serviceId | INTEGER | 关联服务ID（主服务时使用） |
| serviceName | STRING | 服务名称（冗余字段） |
| additionalServiceId | INTEGER | 关联增项服务ID（增项服务时使用） |
| additionalServiceName | STRING | 增项服务名称（冗余字段） |
| startTime | DATE | 服务开始时间 |
| endTime | DATE | 服务结束时间 |
| duration | INTEGER | 服务时长（分钟） |
| remark | STRING | 备注 |

### 增项服务表更新 (additional_services)

新增字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| needDurationTracking | BOOLEAN | 是否需要统计时长（默认true） |

### 记录类型枚举

```typescript
enum ServiceDurationRecordType {
  MAIN_SERVICE = 'main_service',      // 主服务
  ADDITIONAL_SERVICE = 'additional_service'  // 增项服务
}
```

## 员工端接口

### 1. 开始服务

**接口地址：** `POST /employee/service-duration/start`

**请求参数：**
```json
{
  "orderId": 123,
  "orderDetailId": 456,  // 主服务时必填
  "additionalServiceOrderId": 789,  // 增项服务时必填
  "recordType": "main_service",  // main_service | additional_service
  "serviceId": 10,  // 主服务时必填
  "additionalServiceId": 20,  // 增项服务时必填
  "remark": "开始洗护服务"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "orderId": 123,
    "orderDetailId": 456,
    "employeeId": 1,
    "recordType": "main_service",
    "serviceId": 10,
    "serviceName": "基础洗护",
    "startTime": "2024-12-30T10:00:00.000Z",
    "remark": "开始洗护服务"
  }
}
```

### 2. 结束服务

**接口地址：** `POST /employee/service-duration/end`

**请求参数：**
```json
{
  "recordId": 1,
  "remark": "洗护服务完成"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "orderId": 123,
    "startTime": "2024-12-30T10:00:00.000Z",
    "endTime": "2024-12-30T11:30:00.000Z",
    "duration": 90,
    "remark": "洗护服务完成"
  }
}
```

### 3. 查询订单服务时长记录

**接口地址：** `GET /employee/service-duration/records/{orderId}`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "records": [
      {
        "id": 1,
        "recordType": "main_service",
        "serviceName": "基础洗护",
        "startTime": "2024-12-30T10:00:00.000Z",
        "endTime": "2024-12-30T11:30:00.000Z",
        "duration": 90,
        "remark": "洗护服务完成",
        "employee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138000"
        },
        "service": {
          "id": 10,
          "serviceName": "基础洗护",
          "avgDuration": 85
        },
        "orderDetail": {
          "id": 456,
          "serviceName": "基础洗护",
          "petName": "小白",
          "servicePrice": 50.00
        }
      },
      {
        "id": 2,
        "recordType": "additional_service",
        "serviceName": "指甲修剪",
        "additionalServiceName": "指甲修剪",
        "startTime": "2024-12-30T11:30:00.000Z",
        "endTime": "2024-12-30T11:45:00.000Z",
        "duration": 15,
        "additionalService": {
          "id": 20,
          "name": "指甲修剪",
          "duration": 20,
          "price": 15.00,
          "needDurationTracking": true
        },
        "additionalServiceOrder": {
          "id": 789,
          "totalFee": 15.00,
          "status": "已完成",
          "originalPrice": 15.00
        }
      }
    ],
    "statistics": {
      "totalRecords": 2,
      "completedRecords": 2,
      "inProgressRecords": 0,
      "totalDuration": 105,
      "mainServiceDuration": 90,
      "additionalServiceDuration": 15,
      "mainServiceCount": 1,
      "additionalServiceCount": 1
    }
  }
}
```

### 4. 获取当前进行中的服务

**接口地址：** `GET /employee/service-duration/current`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "employeeId": 1,
    "currentServices": [
      {
        "id": 1,
        "recordType": "main_service",
        "serviceName": "基础洗护",
        "startTime": "2024-12-30T10:00:00.000Z",
        "currentDuration": 45,
        "expectedDuration": 85,
        "order": {
          "id": 123,
          "sn": "20241230001",
          "status": "服务中",
          "customer": {
            "id": 1,
            "nickname": "张三",
            "phone": "13800138000"
          }
        },
        "service": {
          "id": 10,
          "serviceName": "基础洗护",
          "avgDuration": 85
        },
        "orderDetail": {
          "id": 456,
          "serviceName": "基础洗护",
          "petName": "小白",
          "servicePrice": 50.00
        }
      }
    ],
    "totalCurrentServices": 1
  }
}
```

### 5. 查询我的服务时长记录

**接口地址：** `GET /employee/service-duration/my-records`

**查询参数：**
- orderId: 订单ID（可选）

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "recordType": "main_service",
      "serviceName": "基础洗护",
      "startTime": "2024-12-30T10:00:00.000Z",
      "endTime": "2024-12-30T11:30:00.000Z",
      "duration": 90
    }
  ]
}
```

## 用户端接口

### 1. 查询订单服务时长记录

**接口地址：** `GET /user/service-duration/records/{orderId}`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "mainServices": [
      {
        "id": 1,
        "serviceName": "基础洗护",
        "startTime": "2024-12-30T10:00:00.000Z",
        "endTime": "2024-12-30T11:30:00.000Z",
        "duration": 90,
        "employee": {
          "id": 1,
          "name": "张师傅"
        }
      }
    ],
    "additionalServices": [
      {
        "id": 2,
        "additionalServiceName": "指甲修剪",
        "startTime": "2024-12-30T11:30:00.000Z",
        "endTime": "2024-12-30T11:45:00.000Z",
        "duration": 15,
        "employee": {
          "id": 1,
          "name": "张师傅"
        }
      }
    ],
    "totalRecords": 2,
    "completedRecords": 2,
    "totalDuration": 105
  }
}
```

### 2. 获取订单服务时长汇总

**接口地址：** `GET /user/service-duration/summary/{orderId}`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "orderSn": "20241230001",
    "orderStatus": "已完成",
    "services": [
      {
        "type": "main_service",
        "serviceId": 10,
        "serviceName": "基础洗护",
        "employeeName": "张师傅",
        "totalDuration": 90,
        "records": [
          {
            "id": 1,
            "startTime": "2024-12-30T10:00:00.000Z",
            "endTime": "2024-12-30T11:30:00.000Z",
            "duration": 90,
            "remark": "洗护服务完成"
          }
        ]
      }
    ],
    "summary": {
      "totalServices": 2,
      "totalDuration": 105,
      "mainServices": 1,
      "additionalServices": 1
    }
  }
}
```

### 3. 获取订单时长统计详情

**接口地址：** `GET /user/service-duration/duration-statistics/{orderId}`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "totalDuration": 105,
    "serviceSpanDuration": 120,
    "mainServiceDuration": 90,
    "additionalServiceDuration": 15,
    "mainServiceCount": 1,
    "additionalServiceCount": 1,
    "records": [
      {
        "id": 1,
        "recordType": "main_service",
        "serviceName": "基础洗护",
        "startTime": "2024-12-30T10:00:00.000Z",
        "endTime": "2024-12-30T11:30:00.000Z",
        "duration": 90,
        "remark": "洗护服务完成"
      },
      {
        "id": 2,
        "recordType": "additional_service",
        "serviceName": "指甲修剪",
        "startTime": "2024-12-30T11:30:00.000Z",
        "endTime": "2024-12-30T11:45:00.000Z",
        "duration": 15,
        "remark": "指甲修剪完成"
      }
    ]
  }
}
```

## 管理端接口

### 1. 查询服务时长记录

**接口地址：** `GET /admin/service-duration-statistics/records`

**查询参数：**
- orderId: 订单ID
- employeeId: 员工ID
- serviceId: 服务ID
- additionalServiceId: 增项服务ID
- startDate: 开始日期
- endDate: 结束日期
- page: 页码
- pageSize: 每页数量

### 2. 服务时长统计

**接口地址：** `GET /admin/service-duration-statistics/service-statistics`

**查询参数：**
- serviceId: 服务ID
- startDate: 开始日期
- endDate: 结束日期

### 3. 员工服务时长统计

**接口地址：** `GET /admin/service-duration-statistics/employee-statistics`

**查询参数：**
- employeeId: 员工ID
- startDate: 开始日期
- endDate: 结束日期

### 4. 获取订单时长统计详情

**接口地址：** `GET /admin/service-duration-statistics/order-duration/{orderId}`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "totalDuration": 105,
    "serviceSpanDuration": 120,
    "mainServiceDuration": 90,
    "additionalServiceDuration": 15,
    "mainServiceCount": 1,
    "additionalServiceCount": 1,
    "records": [...]
  }
}
```

## 业务逻辑说明

### 1. 时长统计机制

- **服务项时长**：每次服务结束后，系统会自动计算该服务最近10次的平均时长
- **主服务平均时长**：更新到 `services.avgDuration` 字段
- **增项服务平均时长**：更新到 `additional_services.duration` 字段
- **订单总时长**：自动计算并更新到 `orders.actualServiceDuration` 字段

### 2. 增项服务时长控制

- 通过 `additional_services.needDurationTracking` 字段控制是否需要统计时长
- 默认值为 `true`，表示需要统计时长
- 设置为 `false` 的增项服务不能开始时长记录

### 3. 权限控制

- 员工只能操作自己负责的订单
- 用户只能查看自己的订单服务记录
- 管理员可以查看所有记录和统计数据

### 4. 数据完整性

- 服务名称采用冗余存储，确保服务被删除后记录仍可查看
- 支持主服务和增项服务的时长分别记录
- 记录开始时间和结束时间，自动计算时长
- 自动计算和更新订单总服务时长

### 5. 异常处理

- 同一服务项目不能重复开始（必须先结束当前服务）
- 只有服务中的订单才能开始服务
- 员工只能操作自己负责的订单
- 不需要统计时长的增项服务无法开始时长记录

### 6. 时长计算说明

- **总服务时长**：所有服务项目和增项服务的累计工作时间
- **服务跨度时长**：从开始服务到结束服务的总时间（包含间隔时间）
- **平均时长**：基于最近10次记录计算，用于服务时间预估
