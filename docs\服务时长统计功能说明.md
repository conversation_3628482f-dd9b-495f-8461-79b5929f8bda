# 服务时长统计功能说明

## 功能概述

服务时长统计模块用于记录和统计每个服务项目和增项服务的具体用时，帮助员工和管理层了解服务效率，优化服务流程。

## 主要功能

### 1. 自动计时
- 员工开始服务时自动开始计时
- 支持主服务和增项服务的分别计时
- 实时显示当前服务用时

### 2. 服务时长记录
- 记录每个服务的开始时间和结束时间
- 自动计算服务时长
- 支持添加备注信息

### 3. 统计查看
- 查看当前进行中的服务
- 查看历史服务时长记录
- 按订单查看所有服务时长

## 使用流程

### 员工端操作

#### 1. 开始服务
1. 在订单列表页面点击"开始服务"
2. 系统自动开始主服务时长统计
3. 可选择直接开始或上传服务前照片

#### 2. 查看当前服务
1. 进入"我的"页面
2. 点击"服务时长"菜单
3. 查看当前进行中的服务列表
4. 实时显示已用时长

#### 3. 结束服务
1. 在服务时长页面点击"结束计时"
2. 或在订单详情页面的服务时长模块操作
3. 确认结束后自动计算总用时

#### 4. 查看历史记录
1. 在订单详情页面查看"服务时长统计"模块
2. 点击展开查看详细记录
3. 显示已完成和进行中的服务

## 页面说明

### 1. 服务时长统计页面 (`/pages/serviceDuration/index`)
- **功能**: 显示当前进行中的服务列表
- **特点**: 
  - 实时更新服务时长
  - 支持下拉刷新
  - 可直接结束计时
  - 点击卡片查看订单详情

### 2. 订单详情页面服务时长模块
- **功能**: 显示当前订单的所有服务时长记录
- **特点**:
  - 可折叠展开
  - 区分进行中和已完成的服务
  - 支持结束当前服务计时

## API接口

### 1. 开始服务时长统计
```
POST /employee/service-duration/start
```

### 2. 结束服务时长统计
```
POST /employee/service-duration/end
```

### 3. 查询订单服务时长记录
```
GET /employee/service-duration/records/{orderId}
```

### 4. 获取当前进行中的服务
```
GET /employee/service-duration/current
```

## 数据结构

### 服务时长记录
```javascript
{
  id: 1,                              // 记录ID
  orderId: 123,                       // 订单ID
  orderDetailId: 456,                 // 订单详情ID（主服务）
  additionalServiceOrderId: 789,      // 增项服务订单ID（增项服务）
  employeeId: 1,                      // 员工ID
  recordType: "main_service",         // 记录类型
  serviceId: 10,                      // 服务ID
  serviceName: "基础洗护",            // 服务名称
  startTime: "2024-12-30T10:00:00Z",  // 开始时间
  endTime: "2024-12-30T11:30:00Z",    // 结束时间
  duration: 90,                       // 时长（分钟）
  remark: "服务完成"                  // 备注
}
```

## 注意事项

1. **自动计时**: 开始服务时会自动开始计时，无需手动操作
2. **实时更新**: 服务时长每分钟自动更新一次
3. **数据准确性**: 确保网络连接稳定，避免计时数据丢失
4. **及时结束**: 服务完成后请及时点击"结束计时"
5. **多服务支持**: 支持同时进行多个服务的计时

## 样式特点

- **现代化设计**: 采用渐变色和圆角设计
- **清晰层次**: 不同状态的服务用不同颜色区分
- **响应式布局**: 适配不同屏幕尺寸
- **交互友好**: 点击反馈和动画效果

## 技术实现

- **前端**: 微信小程序原生开发
- **状态管理**: 页面级状态管理
- **定时器**: 使用setInterval实现实时更新
- **API调用**: 基于Promise的异步请求
- **错误处理**: 完善的错误提示和处理机制
