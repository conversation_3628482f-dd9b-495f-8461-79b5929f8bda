<view class="container">
  <view class="order-info">
    <view class="order-content" data-orderId="{{orderDetail.id}}" bind:tap="viewOrderDetail">
      <image class="product-image" src="{{orderDetail.productImage}}"></image>
      <view class="product-info">
        <view class="flex align-center justify-between">
          <text class="product-name">{{orderDetail.productName}}</text>
        </view>
        <view class="flex align-center justify-between">
          <view class="product-service" wx:if="{{orderInfo.additionalServices && orderInfo.additionalServices.length > 0}}">
            增项服务：<text wx:for="{{orderInfo.additionalServices}}" wx:for-item="val" wx:key="val">{{val}}{{index < orderInfo.additionalServices.length-1 ? '、':''}}</text>
          </view>
          <view class="product-service" wx:else>增项服务：无</view>
        </view>
      </view>
    </view>

    <view class="order-details">
      <view class="detail-item">
        <text class="label">订单编号</text>
        <text class="content">{{orderDetail.sn}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务宠物</text>
        <text class="content">{{orderDetail.petName}}</text>
      </view>
      <view class="detail-item">
        <text class="label">宠物主人</text>
        <text class="content">{{orderDetail.customer.nickname || orderDetail.customer.name}}</text>
      </view>
      <view class="detail-item">
        <text class="label">联系电话</text>
        <text class="content">{{orderDetail.customer.phone || orderDetail.customer.mobile}}</text>
      </view>
      <view class="detail-item">
        <view class="label-row">
          <text class="label">期望上门时间</text>
          <view wx:if="{{orderDetail.status === '待服务'}}" class="edit-btn" bindtap="reschedule">
            <text class="edit-text">修改</text>
          </view>
        </view>
        <text class="content">{{orderDetail.serviceTime}}</text>
      </view>
      <view class="detail-item">
        <view class="label-row">
          <text class="label">服务地址</text>
          <view wx:if="{{orderDetail.status === '待服务'}}" class="edit-btn" bindtap="editServiceAddress">
            <text class="edit-text">修改</text>
          </view>
        </view>
        <view class="content" bindtap="openNavigation" data-address="{{orderDetail.addressDetail}}" data-remark="{{orderDetail.addressRemark}}" data-latitude="{{orderDetail.latitude}}" data-longitude="{{orderDetail.longitude}}">
          <text>{{orderDetail.addressDetail}}</text>
          <text>📍</text>
        </view>
      </view>
      <view class="detail-item">
        <text class="label">最近出入口</text>
        <text class="content">{{orderDetail.addressRemark}}</text>
      </view>
      <view class="detail-item">
        <text class="label">创建时间</text>
        <text class="content">{{orderDetail.createdAt}}</text>
      </view>
      <!-- 用户备注 -->
      <view class="detail-item" wx:if="{{orderDetail.userRemark}}">
        <text class="label">用户备注</text>
        <text class="content user-remark">{{orderDetail.userRemark}}</text>
      </view>
      <!-- 金额信息 -->
      <view class="detail-item" wx:if="{{orderDetail.originalPrice}}">
        <text class="label">原价</text>
        <text class="content original-price">¥{{orderDetail.originalPrice}}</text>
      </view>
      <view class="detail-item">
        <text class="label">实付金额</text>
        <text class="content paid-money">{{orderDetail.totalFee}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">付款时间</text>
        <text class="content">{{orderDetail.orderNumber}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">服务时间</text>
        <text class="content">{{orderDetail.orderNumber}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">成交时间</text>
        <text class="content">{{orderDetail.orderNumber}}</text>
      </view>
    </view>
  </view>

  <!-- 服务评价区域 -->
  <view class="review-section" wx:if="{{orderDetail.status === '已完成' || orderDetail.status === '已评价'}}">
    <view class="section-title">服务评价</view>

    <!-- 评价加载中 -->
    <view class="review-loading" wx:if="{{reviewLoading}}">
      <text class="loading-text">评价加载中...</text>
    </view>

    <!-- 有评价时显示评价内容 -->
    <view class="review-content" wx:elif="{{hasReview && reviewData}}">
      <view class="review-header">
        <view class="rating-section">
          <text class="rating-label">服务评分：</text>
          <text class="rating-stars">{{reviewData.ratingStars}}</text>
          <text class="rating-score">({{reviewData.rating}}分)</text>
        </view>
        <text class="review-time">{{reviewData.createdAt}}</text>
      </view>

      <!-- 评价内容 -->
      <view class="review-text" wx:if="{{reviewData.content}}">
        <text class="review-content-text">{{reviewData.content}}</text>
      </view>

      <!-- 评价图片 -->
      <view class="review-images" wx:if="{{reviewData.images && reviewData.images.length > 0}}">
        <view class="images-title">评价图片：</view>
        <view class="images-grid">
          <image
            wx:for="{{reviewData.images}}"
            wx:key="index"
            src="{{item}}"
            class="review-image"
            mode="aspectFill"
            bindtap="previewImage"
            data-current="{{item}}"
            data-urls="{{reviewData.images}}"
          ></image>
        </view>
      </view>
    </view>

    <!-- 无评价时显示提示 -->
    <view class="no-review" wx:else>
      <text class="no-review-text">客户暂未评价</text>
    </view>
  </view>

  <!-- 服务时长统计 -->
  <view class="service-duration-section" wx:if="{{orderDetail.status === '服务中' || serviceDurationRecords.length > 0}}">
    <view class="section-title" bindtap="toggleServiceDuration">
      <text>服务时长统计</text>
      <text class="toggle-icon">{{showServiceDuration ? '▼' : '▶'}}</text>
    </view>

    <view wx:if="{{showServiceDuration}}">
      <!-- 主服务时长统计 -->
      <view class="main-service-duration" wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}">
        <view class="service-section-title">主服务</view>
        <view class="service-duration-item main-service" wx:for="{{orderDetail.orderDetails}}" wx:key="id">
          <view class="duration-info">
            <view class="service-name-row">
              <text class="service-name">{{item.service.serviceName}}</text>
              <text class="service-status {{getMainServiceStatus(item.id)}}">{{getMainServiceStatusText(item.id)}}</text>
            </view>
            <view class="time-info" wx:if="{{getMainServiceRecord(item.id)}}">
              <text class="start-time" wx:if="{{getMainServiceRecord(item.id).startTime}}">开始时间：{{getMainServiceRecord(item.id).startTime}}</text>
              <text class="current-duration" wx:if="{{getMainServiceRecord(item.id).isRunning}}">已用时：{{getMainServiceRecord(item.id).currentDurationText || '计算中...'}}</text>
              <text class="duration" wx:if="{{!getMainServiceRecord(item.id).isRunning && getMainServiceRecord(item.id).durationText}}">用时：{{getMainServiceRecord(item.id).durationText}}</text>
            </view>
          </view>
          <view class="duration-actions" wx:if="{{orderDetail.status === '服务中'}}">
            <view class="action-btn start-btn"
                  wx:if="{{!getMainServiceRecord(item.id)}}"
                  bindtap="startMainService"
                  data-order-detail-id="{{item.id}}"
                  data-service-id="{{item.service.id}}"
                  data-service-name="{{item.service.serviceName}}">
              开始
            </view>
            <view class="action-btn end-btn"
                  wx:if="{{getMainServiceRecord(item.id) && getMainServiceRecord(item.id).isRunning}}"
                  bindtap="endServiceDuration"
                  data-record-id="{{getMainServiceRecord(item.id).id}}"
                  data-service-name="{{item.service.serviceName}}">
              完成
            </view>
          </view>
        </view>
      </view>

      <!-- 增项服务时长统计 -->
      <view class="additional-service-duration" wx:if="{{allAdditionalServices.length > 0}}">
        <view class="service-section-title">增项服务</view>
        <view class="service-duration-item additional-service" wx:for="{{allAdditionalServices}}" wx:key="id" wx:if="{{item.needDurationTracking !== false}}">
          <view class="duration-info">
            <view class="service-name-row">
              <text class="service-name">{{item.details[0].serviceName || item.serviceName}}</text>
              <text class="service-status {{getAdditionalServiceStatus(item.id)}}">{{getAdditionalServiceStatusText(item.id)}}</text>
            </view>
            <view class="time-info" wx:if="{{getAdditionalServiceRecord(item.id)}}">
              <text class="start-time" wx:if="{{getAdditionalServiceRecord(item.id).startTime}}">开始时间：{{getAdditionalServiceRecord(item.id).startTime}}</text>
              <text class="current-duration" wx:if="{{getAdditionalServiceRecord(item.id).isRunning}}">已用时：{{getAdditionalServiceRecord(item.id).currentDurationText || '计算中...'}}</text>
              <text class="duration" wx:if="{{!getAdditionalServiceRecord(item.id).isRunning && getAdditionalServiceRecord(item.id).durationText}}">用时：{{getAdditionalServiceRecord(item.id).durationText}}</text>
            </view>
          </view>
          <view class="duration-actions" wx:if="{{orderDetail.status === '服务中' && item.status === 'confirmed'}}">
            <view class="action-btn start-btn"
                  wx:if="{{!getAdditionalServiceRecord(item.id)}}"
                  bindtap="startAdditionalService"
                  data-additional-service-id="{{item.id}}"
                  data-service-name="{{item.details[0].serviceName || item.serviceName}}">
              开始
            </view>
            <view class="action-btn end-btn"
                  wx:if="{{getAdditionalServiceRecord(item.id) && getAdditionalServiceRecord(item.id).isRunning}}"
                  bindtap="endServiceDuration"
                  data-record-id="{{getAdditionalServiceRecord(item.id).id}}"
                  data-service-name="{{item.details[0].serviceName || item.serviceName}}">
              完成
            </view>
          </view>
        </view>
      </view>

      <!-- 已完成的服务时长记录汇总 -->
      <view class="completed-services-summary" wx:if="{{serviceDurationRecords.length > 0}}">
        <view class="service-section-title">服务时长汇总</view>
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-label">总服务项目：</text>
            <text class="summary-value">{{serviceDurationRecords.length}}项</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">已完成：</text>
            <text class="summary-value">{{getCompletedServicesCount()}}项</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">总用时：</text>
            <text class="summary-value">{{getTotalDuration()}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 待确认的追加服务 -->
  <view class="additional-services" wx:if="{{pendingAdditionalServices.length > 0}}">
    <view class="section-title">待确认的追加服务</view>
    <view class="service-item" wx:for="{{pendingAdditionalServices}}" wx:key="id">
      <view class="service-info">
        <view class="service-header">
          <text class="service-name">{{item.details[0].serviceName || item.serviceName}}</text>
          <text class="service-status pending">待确认</text>
        </view>

        <!-- 客户信息 -->
        <view class="service-customer-info">
          <text class="customer-label">客户：</text>
          <text class="customer-name">{{item.customerName || item.customer.nickname || '未知客户'}}</text>
          <text class="customer-phone" wx:if="{{item.customerPhone || item.customer.phone}}">({{item.customerPhone || item.customer.phone}})</text>
        </view>

        <!-- 订单信息 -->
        <view class="service-details">
          <view class="detail-row">
            <text class="detail-label">订单号：</text>
            <text class="detail-value">{{item.orderSn || item.sn}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">数量：</text>
            <text class="detail-value">{{item.details[0].quantity || 1}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.createdAt}}">
            <text class="detail-label">申请时间：</text>
            <text class="detail-value detail-time">{{item.createdAt}}</text>
          </view>
        </view>

        <!-- 价格信息 -->
        <view class="service-price-info">
          <view class="price-row" wx:if="{{item.originalPrice && item.originalPrice > 0}}">
            <text class="price-label">原价：</text>
            <text class="original-price">¥{{item.originalPrice}}</text>
          </view>
          <view class="price-row">
            <text class="price-label">实付：</text>
            <text class="current-price">¥{{item.totalFee || item.details[0].servicePrice}}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="service-actions">
        <view class="service-action-btn confirm-btn" bindtap="confirmAdditionalService" data-service="{{item}}">
          确认
        </view>
        <view class="service-action-btn reject-btn" bindtap="rejectAdditionalService" data-service="{{item}}">
          拒绝
        </view>
      </view>
    </view>
  </view>

  <!-- 追加服务记录（非待确认状态） -->
  <view class="all-additional-services" wx:if="{{allAdditionalServices.length > 0}}">
    <view class="section-title">追加服务记录</view>
    <view class="service-item" wx:for="{{allAdditionalServices}}" wx:key="id">
      <view class="service-info">
        <view class="service-header">
          <text class="service-name">{{item.details[0].serviceName || item.serviceName}}</text>
          <text class="service-status {{item.status}}">{{item.statusText}}</text>
        </view>

        <!-- 客户信息 -->
        <view class="service-customer-info">
          <text class="customer-label">客户：</text>
          <text class="customer-name">{{item.customer.name || item.customer.nickname || '未知客户'}}</text>
          <text class="customer-phone" wx:if="{{item.customer.phone}}">({{item.customer.phone}})</text>
        </view>

        <!-- 订单信息 -->
        <view class="service-details">
          <view class="detail-row">
            <text class="detail-label">订单号：</text>
            <text class="detail-value">{{item.sn}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">数量：</text>
            <text class="detail-value">{{item.details[0].quantity || 1}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.createdAt}}">
            <text class="detail-label">申请时间：</text>
            <text class="detail-value detail-time">{{item.createdAt}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.confirmTime}}">
            <text class="detail-label">处理时间：</text>
            <text class="detail-value detail-time">{{item.confirmTime}}</text>
          </view>
        </view>

        <!-- 价格信息 -->
        <view class="service-price-info">
          <view class="price-row" wx:if="{{item.originalPrice && item.originalPrice > 0}}">
            <text class="price-label">原价：</text>
            <text class="original-price">¥{{item.originalPrice}}</text>
          </view>
          <view class="price-row">
            <text class="price-label">实付：</text>
            <text class="current-price">¥{{item.totalFee || item.details[0].servicePrice}}</text>
          </view>
        </view>

        <!-- 拒绝原因（仅拒绝状态显示） -->
        <view class="reject-reason" wx:if="{{item.status === 'rejected' && item.rejectReason}}">
          <text class="reason-label">拒绝原因：</text>
          <text class="reason-text">{{item.rejectReason}}</text>
        </view>
      </view>

      <!-- 操作按钮 - 员工端只在待确认状态显示操作按钮 -->
      <view class="service-actions" wx:if="{{item.status === 'pending_confirm'}}">
        <view class="service-action-btn confirm-btn" bindtap="confirmAdditionalService" data-service="{{item}}">
          确认
        </view>
        <view class="service-action-btn reject-btn" bindtap="rejectAdditionalService" data-service="{{item}}">
          拒绝
        </view>
      </view>
    </view>
  </view>

  <!-- 特殊情况说明 -->
  <view class="special-note-section" wx:if="{{specialNoteData}}">
    <view class="section-title">特殊情况说明</view>
    <view class="special-note-content">
      <view class="note-info">
        <view class="note-header">
          <text class="note-employee">记录员工：{{specialNoteData.employee.name}}</text>
          <text class="note-time">{{specialNoteData.createdAt}}</text>
        </view>

        <!-- 文字内容 -->
        <view class="note-text" wx:if="{{specialNoteData.content}}">
          <text class="content-text">{{specialNoteData.content}}</text>
        </view>

        <!-- 图片内容 -->
        <view class="note-photos" wx:if="{{specialNoteData.photos && specialNoteData.photos.length > 0}}">
          <view class="photos-grid">
            <image
              wx:for="{{specialNoteData.photos}}"
              wx:key="index"
              src="{{item}}"
              class="note-photo"
              mode="aspectFill"
              bindtap="previewSpecialNotePhoto"
              data-url="{{item}}"
              data-urls="{{specialNoteData.photos}}"
            ></image>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="note-actions" wx:if="{{orderDetail.status === '服务中'}}">
        <view class="note-action-btn" bindtap="showSpecialNote">
          编辑说明
        </view>
      </view>

      <!-- 查看按钮（已完成状态） -->
      <view class="note-actions" wx:if="{{orderDetail.status === '已完成' || orderDetail.status === '已评价'}}">
        <view class="note-action-btn view-btn" bindtap="showSpecialNote">
          查看详情
        </view>
      </view>
    </view>
  </view>

  <!-- 特殊情况说明入口（无数据时显示） -->
  <view class="special-note-entry" wx:if="{{!specialNoteData && (orderDetail.status === '服务中')}}">
    <view class="section-title">特殊情况说明</view>
    <view class="entry-content" bindtap="showSpecialNote">
      <view class="entry-icon">📝</view>
      <text class="entry-text">点击添加特殊情况说明</text>
      <view class="entry-arrow">></view>
    </view>
  </view>

  <!-- 更多操作弹窗 -->
  <view wx:if="{{showMoreActions}}" class="more-actions-dropdown">
    <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.orderId}}">
      更改服务地址
    </view>
    <view class="dropdown-item" bindtap="deleteOrder" data-order-id="{{item.orderId}}">
      更换服务人员
    </view>
    <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.orderId}}">
      取消订单
    </view>
  </view>
  <!-- 底部操作按钮容器 -->
  <view class="bottom-action-container" wx:if="{{orderDetail.status === '待服务'}}">
    <view class="action-buttons-row">
      <!-- 只保留联系客户按钮 -->
      <view class="single-action-btn contact-btn" bindtap="contactCustomer" data-order-id="{{orderDetail.orderId}}">
        <image src="//xian7.zos.ctyun.cn/pet/static/dianhua.png" class="btn-icon"></image>
        <text class="btn-text">联系客户</text>
      </view>
    </view>
  </view>

  <!-- 已评价状态的操作按钮 -->
  <view class="bottom-action-container" wx:if="{{orderDetail.status === '已评价'}}">
    <view class="action-buttons-row">
      <view class="single-action-btn review-btn" bindtap="viewReview">
        <text class="btn-text">查看评价</text>
      </view>
    </view>
  </view>

  <!-- 时间选择器 -->
  <custom-picker
    wx:if="{{showTimePicker}}"
    bind:confirm="onTimeSelected"
    bind:cancel="onTimeCancel"
    selectedTime="{{selectedTime}}"
    hasBottomNavigation="{{false}}"
  />

  <!-- 拒绝原因输入模态框 -->
  <view class="reject-modal" wx:if="{{showRejectModal}}">
    <view class="modal-mask" bindtap="cancelReject"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">拒绝原因</text>
      </view>
      <view class="modal-body">
        <textarea
          class="reject-textarea"
          placeholder="请输入拒绝原因..."
          value="{{rejectReason}}"
          bindinput="onRejectReasonInput"
          maxlength="200"
        ></textarea>
        <view class="char-count">{{rejectReason.length}}/200</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="cancelReject">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmReject">确认拒绝</button>
      </view>
    </view>
  </view>

  <!-- 特殊情况说明组件 -->
  <special-note-upload
    wx:if="{{showSpecialNote}}"
    show="{{showSpecialNote}}"
    orderInfo="{{orderDetail}}"
    readonly="{{specialNoteReadonly}}"
    noteData="{{specialNoteData}}"
    bind:confirm="onSpecialNoteConfirm"
    bind:cancel="onSpecialNoteCancel"
    bind:delete="onSpecialNoteDelete"
  />

  <!-- 地址编辑器组件 -->
  <address-editor
    wx:if="{{showAddressEditor}}"
    show="{{showAddressEditor}}"
    orderInfo="{{orderDetail}}"
    bind:confirm="onAddressEditConfirm"
    bind:cancel="onAddressEditCancel"
  />
</view>